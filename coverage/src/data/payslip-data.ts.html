
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/data/payslip-data.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/data</a> payslip-data.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { PayslipEntry } from '../types';
&nbsp;
export const INITIAL_PAYSLIP_DATA: PayslipEntry[] = [
  { date: '2023-09-13', gross: 6173.07, net: 2363.87, espp: 0, roth_e: 1232.61, roth_r: 369.78, total_invest: 1602.39 },
  { date: '2023-09-27', gross: 6173.07, net: 2363.88, espp: 0, roth_e: 1232.61, roth_r: 369.78, total_invest: 1602.39 },
  { date: '2023-10-11', gross: 6173.07, net: 2363.87, espp: 0, roth_e: 1232.61, roth_r: 369.78, total_invest: 1602.39 },
  { date: '2023-10-25', gross: 6173.07, net: 2363.88, espp: 0, roth_e: 1232.61, roth_r: 369.78, total_invest: 1602.39 },
  { date: '2023-11-08', gross: 6173.07, net: 2672.12, espp: 308.65, roth_e: 926.00, roth_r: 277.80, total_invest: 1512.45 },
  { date: '2023-11-22', gross: 6173.07, net: 3143.60, espp: 308.65, roth_e: 617.31, roth_r: 185.19, total_invest: 1111.15 },
  { date: '2023-12-06', gross: 6173.07, net: 5942.55, espp: 0, roth_e: 0, roth_r: 0, total_invest: 0 },
  { date: '2023-12-20', gross: 6173.07, net: 1847.23, espp: 308.65, roth_e: 1543.27, roth_r: 462.98, total_invest: 2314.90 },
  { date: '2024-01-03', gross: 6173.07, net: 1860.45, espp: 617.31, roth_e: 1389.00, roth_r: 416.70, total_invest: 2423.01 },
  { date: '2024-01-17', gross: 6173.08, net: 1621.82, espp: 617.31, roth_e: 1543.27, roth_r: 462.98, total_invest: 2623.56 },
  { date: '2024-01-31', gross: 6173.07, net: 1621.84, espp: 617.31, roth_e: 1543.27, roth_r: 462.98, total_invest: 2623.56 },
  { date: '2024-02-14', gross: 6173.07, net: 1621.83, espp: 617.31, roth_e: 1543.27, roth_r: 462.98, total_invest: 2623.56 },
  { date: '2024-02-28', gross: 6173.07, net: 1621.81, espp: 617.31, roth_e: 1543.27, roth_r: 462.98, total_invest: 2623.56 },
  { date: '2024-03-13', gross: 6173.07, net: 1621.84, espp: 617.31, roth_e: 1543.27, roth_r: 462.98, total_invest: 2623.56 },
  { date: '2024-03-27', gross: 6173.07, net: 1621.83, espp: 617.31, roth_e: 1543.27, roth_r: 462.98, total_invest: 2623.56 },
  { date: '2024-04-10', gross: 6296.14, net: 1642.91, espp: 629.61, roth_e: 1574.03, roth_r: 472.21, total_invest: 2675.85 },
  { date: '2024-04-24', gross: 6419.23, net: 1664.03, espp: 642.00, roth_e: 1605.00, roth_r: 481.50, total_invest: 2728.50 },
  { date: '2024-05-08', gross: 6419.23, net: 1664.04, espp: 642.00, roth_e: 1605.00, roth_r: 481.50, total_invest: 2728.50 },
  { date: '2024-05-22', gross: 6419.23, net: 1664.03, espp: 642.00, roth_e: 1605.00, roth_r: 481.50, total_invest: 2728.50 },
  { date: '2024-06-05', gross: 6419.23, net: 1334.44, espp: 962.88, roth_e: 1605.00, roth_r: 481.50, total_invest: 3049.38 },
  { date: '2024-06-19', gross: 6419.23, net: 1334.47, espp: 962.88, roth_e: 1605.00, roth_r: 481.50, total_invest: 3049.38 },
  { date: '2024-07-03', gross: 6419.23, net: 2889.46, espp: 642.00, roth_e: 642.00, roth_r: 192.60, total_invest: 1476.60 },
  { date: '2024-07-17', gross: 6419.23, net: 3260.22, espp: 642.00, roth_e: 448.40, roth_r: 134.52, total_invest: 1224.92 },
  { date: '2024-07-31', gross: 6419.23, net: 3260.21, espp: 642.00, roth_e: 448.40, roth_r: 134.52, total_invest: 1224.92 },
  { date: '2025-01-15', gross: 6419.23, net: 1344.56, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 },
  { date: '2025-01-29', gross: 6419.23, net: 1305.93, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 },
  { date: '2025-02-12', gross: 6419.23, net: 1287.32, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 },
  { date: '2025-02-26', gross: 6419.23, net: 1287.31, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 },
  { date: '2025-03-12', gross: 6419.23, net: 1287.32, espp: 962.88, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.65 },
  { date: '2025-03-26', gross: 6419.23, net: 1287.33, espp: 962.89, roth_e: 1669.00, roth_r: 256.77, total_invest: 2888.66 },
  { date: '2025-04-09', gross: 6496.29, net: 1296.50, espp: 974.45, roth_e: 1689.04, roth_r: 259.85, total_invest: 2923.34 },
  { date: '2025-04-23', gross: 6323.69, net: 1275.92, espp: 948.55, roth_e: 1645.35, roth_r: 253.13, total_invest: 2847.03 },
  { date: '2025-05-07', gross: 6323.69, net: 1275.93, espp: 948.55, roth_e: 1645.35, roth_r: 253.13, total_invest: 2847.03 },
  { date: '2025-05-21', gross: 6323.69, net: 1275.94, espp: 948.55, roth_e: 1645.35, roth_r: 253.13, total_invest: 2847.03 },
  { date: '2025-06-04', gross: 6323.69, net: 1275.94, espp: 948.55, roth_e: 1645.35, roth_r: 253.13, total_invest: 2847.03 },
  { date: '2025-06-18', gross: 6323.69, net: 1275.96, espp: 948.55, roth_e: 1645.35, roth_r: 253.13, total_invest: 2847.03 },
];
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T02:38:07.858Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    