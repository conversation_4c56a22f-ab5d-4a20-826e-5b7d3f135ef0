
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> src</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">90.37% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>723/800</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">78.82% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>134/170</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">95.5% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>85/89</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">90.27% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>678/751</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="formulas.ts"><a href="formulas.ts.html">formulas.ts</a></td>
	<td data-value="94.3" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.3" class="pct high">94.3%</td>
	<td data-value="193" class="abs high">182/193</td>
	<td data-value="73.91" class="pct medium">73.91%</td>
	<td data-value="23" class="abs medium">17/23</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="17" class="abs high">17/17</td>
	<td data-value="94.14" class="pct high">94.14%</td>
	<td data-value="188" class="abs high">177/188</td>
	</tr>

<tr>
	<td class="file medium" data-value="payslip-parser.ts"><a href="payslip-parser.ts.html">payslip-parser.ts</a></td>
	<td data-value="88.61" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.61" class="pct medium">88.61%</td>
	<td data-value="202" class="abs medium">179/202</td>
	<td data-value="85.91" class="pct medium">85.91%</td>
	<td data-value="71" class="abs medium">61/71</td>
	<td data-value="95" class="pct high">95%</td>
	<td data-value="20" class="abs high">19/20</td>
	<td data-value="87.97" class="pct medium">87.97%</td>
	<td data-value="183" class="abs medium">161/183</td>
	</tr>

<tr>
	<td class="file high" data-value="portfolio-parser.ts"><a href="portfolio-parser.ts.html">portfolio-parser.ts</a></td>
	<td data-value="91.54" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 91%"></div><div class="cover-empty" style="width: 9%"></div></div>
	</td>
	<td data-value="91.54" class="pct high">91.54%</td>
	<td data-value="142" class="abs high">130/142</td>
	<td data-value="83.33" class="pct medium">83.33%</td>
	<td data-value="42" class="abs medium">35/42</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="91.17" class="pct high">91.17%</td>
	<td data-value="136" class="abs high">124/136</td>
	</tr>

<tr>
	<td class="file medium" data-value="wealth-tracker.ts"><a href="wealth-tracker.ts.html">wealth-tracker.ts</a></td>
	<td data-value="88.21" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.21" class="pct medium">88.21%</td>
	<td data-value="263" class="abs medium">232/263</td>
	<td data-value="61.76" class="pct medium">61.76%</td>
	<td data-value="34" class="abs medium">21/34</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="42" class="abs high">39/42</td>
	<td data-value="88.52" class="pct medium">88.52%</td>
	<td data-value="244" class="abs medium">216/244</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-17T02:38:07.830Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    