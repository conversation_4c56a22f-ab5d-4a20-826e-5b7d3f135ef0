import * as fs from 'fs';
import * as readline from 'readline';
import {PortfolioEntry, PayslipEntry, WealthData, ScenarioRates, Statistics} from './types';
import {INITIAL_PORTFOLIO_DATA} from './data/portfolio-data';
import {INITIAL_PAYSLIP_DATA} from './data/payslip-data';
import {WEALTH_CONSTANTS} from './data/constants';

export class WealthTracker {
    private portfolioHistory: PortfolioEntry[] = [...INITIAL_PORTFOLIO_DATA];
    private payslipHistory: PayslipEntry[] = [...INITIAL_PAYSLIP_DATA];

    private readonly ANNUAL_BONUS_GROSS = WEALTH_CONSTANTS.ANNUAL_BONUS_GROSS;
    private readonly ANNUAL_BONUS_NET = WEALTH_CONSTANTS.ANNUAL_BONUS_NET;
    private readonly BONUS_MONTH = WEALTH_CONSTANTS.BONUS_MONTH;
    private readonly PAYCHECKS_PER_YEAR = WEALTH_CONSTANTS.PAYCHECKS_PER_YEAR;
    private readonly MONTHLY_EXPENSES = WEALTH_CONSTANTS.MONTHLY_EXPENSES;

    addPortfolioData(year: number, month: number, trow: number, robinhood: number, etrade: number, teradata: number, fidelity: number = 0): void {
        const dateStr = `${year}-${month.toString().padStart(2, '0')}`;
        const newEntry: PortfolioEntry = {
            date: dateStr,
            trow,
            robinhood,
            etrade,
            teradata,
            fidelity
        };

        this.portfolioHistory.push(newEntry);
        console.log(`
✅ Added portfolio data for ${this.formatDate(dateStr)}`);
        console.log(`   Total Portfolio Value: $${this.getTotalValue(newEntry).toLocaleString()}`);
    }

    addPayslip(dateStr: string, gross: number, net: number, espp: number, rothE: number, rothR: number): void {
        const totalInvest = espp + rothE + rothR;
        const newPayslip: PayslipEntry = {
            date: dateStr,
            gross,
            net,
            espp,
            roth_e: rothE,
            roth_r: rothR,
            total_invest: totalInvest
        };

        this.payslipHistory.push(newPayslip);
        this.payslipHistory.sort((a, b) => a.date.localeCompare(b.date));

        console.log(`
✅ Added payslip for ${dateStr}`);
        console.log(`   Net Pay: $${net.toLocaleString()}`);
        console.log(`   Total Investments: $${totalInvest.toLocaleString()}`);
    }

    async addRecentPayslips(): Promise<void> {
        console.log('\nPORTFOLIO ADD RECENT PAYSLIPS');
        console.log('Enter payslip data (press Enter with empty date to finish)');

        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        const question = (query: string): Promise<string> => {
            return new Promise(resolve => rl.question(query, resolve));
        };

        let count = 0;
        while (count < 4) {
            console.log(`\nPayslip ${count + 1}:`);
            const dateStr = await question('Date (YYYY-MM-DD): ');

            if (!dateStr.trim()) {
                break;
            }

            try {
                const gross = parseFloat(await question('Gross Pay: $'));
                const net = parseFloat(await question('Net Pay: $'));
                const espp = parseFloat(await question('ESPP Deduction: $'));
                const rothE = parseFloat(await question('GapShare Roth E: $'));
                const rothR = parseFloat(await question('GapShare Roth R: $'));

                this.addPayslip(dateStr, gross, net, espp, rothE, rothR);
                count++;

            } catch (error) {
                console.log('Invalid input. Please enter numbers only.');
            }
        }

        rl.close();
    }

    calculateAverageMonthlyContribution(): number {
        if (this.payslipHistory.length === 0) {
            return 6248;
        }

        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const recentPayslips = this.payslipHistory.filter(p =>
            new Date(p.date) >= sixMonthsAgo
        );

        const payslipsToUse = recentPayslips.length > 0 ? recentPayslips : this.payslipHistory.slice(-12);

        if (payslipsToUse.length > 0) {
            const avgInvestment = payslipsToUse.reduce((sum, p) => sum + p.total_invest, 0) / payslipsToUse.length;
            return avgInvestment * this.PAYCHECKS_PER_YEAR / 12;
        }

        return 6248;
    }

    private getTotalValue(entry: PortfolioEntry): number {
        return entry.trow + entry.robinhood + entry.etrade + entry.teradata + (entry.fidelity || 0);
    }

    private getMonthsElapsed(): number[] {
        const monthsElapsed: number[] = [];
        const startDate = new Date(this.portfolioHistory[0].date + '-01');

        for (const entry of this.portfolioHistory) {
            const currentDate = new Date(entry.date + '-01');
            const monthsDiff = (currentDate.getFullYear() - startDate.getFullYear()) * 12 +
                (currentDate.getMonth() - startDate.getMonth());
            monthsElapsed.push(monthsDiff);
        }

        return monthsElapsed;
    }

    private formatDate(dateStr: string): string {
        const date = new Date(dateStr + '-01');
        return date.toLocaleDateString('en-US', {year: 'numeric', month: 'long'});
    }

    calculateIrr(): number {
        const values = this.portfolioHistory.map(entry => this.getTotalValue(entry));
        const months = this.getMonthsElapsed();
        const monthlyContribution = this.calculateAverageMonthlyContribution();

        let irr = 0.02;

        for (let iteration = 0; iteration < 100; iteration++) {
            let npv = values[0];
            let dnpv = 0;

            for (let i = 1; i < values.length; i++) {
                const n = months[i];
                const monthDiff = months[i] - months[i - 1];

                let fvExpected = values[i - 1] * Math.pow(1 + irr, monthDiff);
                fvExpected += irr !== 0 ?
                    monthlyContribution * (Math.pow(1 + irr, monthDiff) - 1) / irr :
                    monthlyContribution * monthDiff;

                if (this.crossesBonusMonth(i)) {
                    const monthsAfterBonus = this.monthsAfterBonus(i);
                    fvExpected += this.ANNUAL_BONUS_NET * Math.pow(1 + irr, monthsAfterBonus);
                }

                const residual = values[i] - fvExpected;
                const discountFactor = Math.pow(1 + irr, -n);

                npv += residual * discountFactor;
                dnpv -= n * residual * discountFactor / (1 + irr);
            }

            if (Math.abs(dnpv) < 1e-10) break;

            const irrNew = irr - npv / dnpv;

            if (Math.abs(irrNew - irr) < 1e-8) {
                return irrNew;
            }

            irr = irrNew;
        }

        return irr;
    }

    calculatePeriodReturns(): number[] {
        const values = this.portfolioHistory.map(entry => this.getTotalValue(entry));
        const months = this.getMonthsElapsed();
        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const monthlyReturns: number[] = [];

        for (let i = 1; i < values.length; i++) {
            const monthDiff = months[i] - months[i - 1];
            let contribution = monthDiff * monthlyContribution;

            if (this.crossesBonusMonth(i)) {
                contribution += this.ANNUAL_BONUS_NET;
            }

            const startValue = values[i - 1];
            const endValue = values[i];
            const investmentGain = endValue - startValue - contribution;

            const adjustedStart = startValue + 0.5 * contribution;

            if (adjustedStart > 0) {
                const periodReturn = investmentGain / adjustedStart;
                const monthlyReturn = Math.pow(1 + periodReturn, 1 / monthDiff) - 1;
                monthlyReturns.push(monthlyReturn);
            }
        }

        return monthlyReturns;
    }

    private crossesBonusMonth(index: number): boolean {
        if (index === 0) return false;

        const prevDate = new Date(this.portfolioHistory[index - 1].date + '-01');
        const currDate = new Date(this.portfolioHistory[index].date + '-01');

        for (let year = prevDate.getFullYear(); year <= currDate.getFullYear(); year++) {
            const bonusDate = new Date(year, this.BONUS_MONTH - 1, 1);
            if (prevDate < bonusDate && bonusDate <= currDate) {
                return true;
            }
        }

        return false;
    }

    private monthsAfterBonus(index: number): number {
        const currDate = new Date(this.portfolioHistory[index].date + '-01');

        let bonusDate: Date;
        if (currDate.getMonth() + 1 >= this.BONUS_MONTH) {
            bonusDate = new Date(currDate.getFullYear(), this.BONUS_MONTH - 1, 1);
        } else {
            bonusDate = new Date(currDate.getFullYear() - 1, this.BONUS_MONTH - 1, 1);
        }

        const monthsDiff = (currDate.getFullYear() - bonusDate.getFullYear()) * 12 +
            (currDate.getMonth() - bonusDate.getMonth());
        return monthsDiff;
    }

    calculateStatistics(): Statistics {
        const monthlyReturns = this.calculatePeriodReturns();

        if (monthlyReturns.length === 0) {
            return {meanReturn: 0, stdDev: 0};
        }

        const meanReturn = monthlyReturns.reduce((sum, r) => sum + r, 0) / monthlyReturns.length;

        let stdDev = 0;
        if (monthlyReturns.length > 1) {
            const variance = monthlyReturns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) /
                (monthlyReturns.length - 1);
            stdDev = Math.sqrt(variance);
        }

        return {meanReturn, stdDev};
    }

    projectFuture(target: number, monthlyRate: number, monthlyContribution: number): number {
        const currentValue = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]);

        if (monthlyRate <= 0) {
            const needed = target - currentValue;
            return Math.ceil(needed / monthlyContribution);
        } else {
            const numerator = Math.log((target * monthlyRate + monthlyContribution) /
                (currentValue * monthlyRate + monthlyContribution));
            const denominator = Math.log(1 + monthlyRate);
            return Math.max(0, Math.ceil(numerator / denominator));
        }
    }

    analyzePayslips(): void {
        console.log('\n💰 PAYSLIP ANALYSIS');
        console.log('-'.repeat(60));

        if (this.payslipHistory.length === 0) {
            console.log('No payslip data available.');
            return;
        }

        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const recentPayslips = this.payslipHistory.filter(p =>
            new Date(p.date) >= sixMonthsAgo
        );

        if (recentPayslips.length > 0) {
            const avgGross = recentPayslips.reduce((sum, p) => sum + p.gross, 0) / recentPayslips.length;
            const avgNet = recentPayslips.reduce((sum, p) => sum + p.net, 0) / recentPayslips.length;
            const avgInvest = recentPayslips.reduce((sum, p) => sum + p.total_invest, 0) / recentPayslips.length;

            console.log(`Recent Averages (last ${recentPayslips.length} payslips):`);
            console.log(`  Gross Pay:        $${avgGross.toLocaleString().padStart(8)} bi-weekly`);
            console.log(`  Net Pay:          $${avgNet.toLocaleString().padStart(8)} bi-weekly`);
            console.log(`  Investments:      $${avgInvest.toLocaleString().padStart(8)} bi-weekly`);
            console.log(`  Monthly Invest:   $${(avgInvest * this.PAYCHECKS_PER_YEAR / 12).toLocaleString().padStart(8)}`);

            const avgEspp = recentPayslips.reduce((sum, p) => sum + p.espp, 0) / recentPayslips.length;
            const avgRothE = recentPayslips.reduce((sum, p) => sum + p.roth_e, 0) / recentPayslips.length;
            const avgRothR = recentPayslips.reduce((sum, p) => sum + p.roth_r, 0) / recentPayslips.length;

            console.log('\nInvestment Breakdown:');
            console.log(`  ESPP:             $${avgEspp.toLocaleString().padStart(8)} (${(avgEspp / avgGross * 100).toFixed(1)}%)`);
            console.log(`  Roth E:           $${avgRothE.toLocaleString().padStart(8)} (${(avgRothE / avgGross * 100).toFixed(1)}%)`);
            console.log(`  Roth R:           $${avgRothR.toLocaleString().padStart(8)} (${(avgRothR / avgGross * 100).toFixed(1)}%)`);
            console.log(`  Total:            $${avgInvest.toLocaleString().padStart(8)} (${(avgInvest / avgGross * 100).toFixed(1)}%)`);
        }
    }

    analyzeAndProject(): void {
        console.log('\n' + '='.repeat(60));
        console.log('WEALTH TRACKER ANALYSIS');
        console.log('='.repeat(60));

        const currentEntry = this.portfolioHistory[this.portfolioHistory.length - 1];
        const currentValue = this.getTotalValue(currentEntry);
        const currentDate = this.formatDate(currentEntry.date);

        console.log(`\nCURRENT STATUS (${currentDate})`);
        console.log(`   T.Rowe Retirement: $${currentEntry.trow.toLocaleString().padStart(12)}`);
        console.log(`   RobinHood:         $${currentEntry.robinhood.toLocaleString().padStart(12)}`);
        console.log(`   E*Trade:           $${currentEntry.etrade.toLocaleString().padStart(12)}`);
        console.log(`   Teradata 401k:     $${currentEntry.teradata.toLocaleString().padStart(12)}`);
        if (currentEntry.fidelity) {
            console.log(`   Fidelity:          $${currentEntry.fidelity.toLocaleString().padStart(12)}`);
        }
        console.log(`   ${'-'.repeat(35)}`);
        console.log(`   TOTAL:             $${currentValue.toLocaleString().padStart(12)}`);

        const monthlyContribution = this.calculateAverageMonthlyContribution();
        const irr = this.calculateIrr();
        const {meanReturn, stdDev} = this.calculateStatistics();

        console.log(`\nPERFORMANCE METRICS`);
        console.log(`   Internal Rate of Return (IRR):  ${(irr * 100).toFixed(2).padStart(6)}% monthly`);
        console.log(`   Mean Monthly Return:            ${(meanReturn * 100).toFixed(2).padStart(6)}% monthly`);
        console.log(`   Standard Deviation:             ${(stdDev * 100).toFixed(2).padStart(6)}% monthly`);
        console.log(`   Annualized IRR:                 ${((Math.pow(1 + irr, 12) - 1) * 100).toFixed(1).padStart(6)}%`);

        console.log(`\nCONTRIBUTION ANALYSIS`);
        console.log(`   Monthly Investment (from payslips): $${monthlyContribution.toLocaleString().padStart(8)}`);
        console.log(`   Annual Bonus (net):                 $${this.ANNUAL_BONUS_NET.toLocaleString().padStart(8)}`);
        console.log(`   Monthly Living Expenses:            $${this.MONTHLY_EXPENSES.toLocaleString().padStart(8)}`);

        const scenarios: ScenarioRates = {
            'Conservative': Math.max(0.005, irr - stdDev),
            'Baseline': irr,
            'Optimistic': meanReturn
        };

        console.log(`\nSCENARIO RATES`);
        Object.entries(scenarios).forEach(([name, rate]) => {
            console.log(`   ${name.padEnd(14)} ${(rate * 100).toFixed(2).padStart(6)}% monthly (${((Math.pow(1 + rate, 12) - 1) * 100).toFixed(1).padStart(5)}% annual)`);
        });

        const targets = [1_000_000, 1_500_000, 2_000_000];

        console.log(`\nWEALTH MILESTONES`);

        targets.forEach(target => {
            console.log(`\n   Target: $${(target / 1e6).toFixed(1)} Million`);
            console.log('   ' + '-'.repeat(50));

            Object.entries(scenarios).forEach(([scenarioName, rate]) => {
                const months = this.projectFuture(target, rate, monthlyContribution);

                const currentDateObj = new Date(currentEntry.date + '-01');
                const targetDate = new Date(currentDateObj.getFullYear(), currentDateObj.getMonth() + months, 1);

                console.log(`   ${scenarioName.padEnd(14)} ${months.toString().padStart(3)} months  →  ${targetDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                })}`);
            });
        });

        this.analyzePayslips();
        this.showGrowthAttribution(monthlyContribution);
    }

    private showGrowthAttribution(monthlyContribution: number): void {
        const totalGrowth = this.getTotalValue(this.portfolioHistory[this.portfolioHistory.length - 1]) -
            this.getTotalValue(this.portfolioHistory[0]);
        const monthsElapsed = this.getMonthsElapsed();
        const monthsElapsedTotal = monthsElapsed[monthsElapsed.length - 1];

        let estimatedContributions = monthsElapsedTotal * monthlyContribution;
        const yearsElapsed = Math.floor(monthsElapsedTotal / 12);
        estimatedContributions += yearsElapsed * this.ANNUAL_BONUS_NET;

        const investmentReturns = totalGrowth - estimatedContributions;

        console.log(`\nGROWTH ATTRIBUTION (Since ${this.formatDate(this.portfolioHistory[0].date)})`);
        console.log(`   Total Growth:         $${totalGrowth.toLocaleString().padStart(12)}`);
        console.log(`   From Contributions:   $${estimatedContributions.toLocaleString().padStart(12)} (${(estimatedContributions / totalGrowth * 100).toFixed(1).padStart(5)}%)`);
        console.log(`   From Returns:         $${investmentReturns.toLocaleString().padStart(12)} (${(investmentReturns / totalGrowth * 100).toFixed(1).padStart(5)}%)`);
    }

    saveData(filename: string = 'wealth_tracker_data.json'): void {
        const data: WealthData = {
            portfolio_history: this.portfolioHistory,
            payslip_history: this.payslipHistory
        };

        fs.writeFileSync(filename, JSON.stringify(data, null, 2));
        console.log(`\nData saved to ${filename}`);
    }

    loadData(filename: string = 'wealth_tracker_data.json'): void {
        try {
            const data = JSON.parse(fs.readFileSync(filename, 'utf8')) as WealthData;
            this.portfolioHistory = data.portfolio_history || this.portfolioHistory;
            this.payslipHistory = data.payslip_history || this.payslipHistory;
            console.log(`\nData loaded from ${filename}`);
        } catch (error) {
            console.log(`\nNo saved data found at ${filename}`);
        }
    }

    showPortfolioHistory(): void {
        console.log('\nPORTFOLIO HISTORY');
        console.log('-'.repeat(80));
        console.log('Date         T.Rowe      RobinHood      E*Trade       Teradata        Total');
        console.log('-'.repeat(80));

        this.portfolioHistory.forEach(entry => {
            const total = this.getTotalValue(entry);
            const date = this.formatDate(entry.date);
            console.log(`${date.padEnd(12)} $${entry.trow.toLocaleString().padStart(11)} $${entry.robinhood.toLocaleString().padStart(11)} $${entry.etrade.toLocaleString().padStart(11)} $${entry.teradata.toLocaleString().padStart(11)} $${total.toLocaleString().padStart(11)}`);
        });
    }

    showRecentPayslips(): void {
        console.log('\nRECENT PAYSLIPS (Last 10)');
        console.log('-'.repeat(85));
        console.log('Date         Gross       Net       ESPP      Roth E     Roth R   Total Inv');
        console.log('-'.repeat(85));

        const recentPayslips = this.payslipHistory.slice(-10);
        recentPayslips.forEach(p => {
            console.log(`${p.date.padEnd(12)} $${p.gross.toLocaleString().padStart(9)} $${p.net.toLocaleString().padStart(9)} $${p.espp.toLocaleString().padStart(9)} $${p.roth_e.toLocaleString().padStart(9)} $${p.roth_r.toLocaleString().padStart(9)} $${p.total_invest.toLocaleString().padStart(9)}`);
        });
    }
}